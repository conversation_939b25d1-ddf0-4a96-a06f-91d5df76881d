# MDX 多语言内容生成模块使用指南

## 概述

本项目已成功集成了基于 MDX 的多语言内容管理系统，支持博客、产品和案例研究三种内容类型。该系统与现有的数据库驱动的 posts 系统并存，不会影响现有功能。

## 目录结构

```text
content/
├── blogs/
│   ├── en/
│   │   └── *.mdx
│   └── zh/
│       └── *.mdx
├── products/
│   ├── en/
│   │   └── *.mdx
│   └── zh/
│       └── *.mdx
└── case-studies/
    ├── en/
    │   └── *.mdx
    └── zh/
        └── *.mdx
```

## 页面路由

### 新增的 MDX 内容路由

- `/blogs` - 博客列表页
- `/blogs/[slug]` - 博客详情页
- `/products` - 产品列表页
- `/products/[slug]` - 产品详情页
- `/case-studies` - 案例研究列表页
- `/case-studies/[slug]` - 案例研究详情页

### 现有的数据库驱动路由（保持不变）

- `/posts` - 文章列表页
- `/posts/[slug]` - 文章详情页

## MDX 文件格式

每个 MDX 文件都需要包含 frontmatter 元数据：

```mdx
---
title: "文章标题"
slug: "url-slug"
description: "文章描述"
coverImage: "/imgs/cover.jpg"
author: "作者名称"
authorImage: "/imgs/author.jpg"
publishedAt: "2025-01-17"
featured: true
tags: ["标签1", "标签2"]
---

# 文章内容

这里是 MDX 格式的文章内容...
```

## 如何添加新内容

### 1. 创建 MDX 文件

在相应的目录中创建新的 MDX 文件：

```bash
# 博客文章
content/blogs/en/my-new-post.mdx
content/blogs/zh/wo-de-xin-wen-zhang.mdx

# 产品页面
content/products/en/new-product.mdx
content/products/zh/xin-chan-pin.mdx

# 案例研究
content/case-studies/en/case-study.mdx
content/case-studies/zh/an-li-yan-jiu.mdx
```

### 2. 编写内容

使用 MDX 格式编写内容，支持：

- Markdown 语法
- React 组件
- 自定义样式

### 3. 构建和部署

```bash
# 开发模式
pnpm dev

# 构建生产版本
pnpm build
```

## 技术栈

- **Contentlayer**: MDX 内容处理
- **Next.js**: 静态生成和路由
- **next-intl**: 国际化支持
- **TypeScript**: 类型安全

## 自动化功能

### SEO 优化

- 自动生成 sitemap.xml
- 自动生成 RSS feeds（支持多语言）
- 元数据优化

### 类型安全

- 类型安全的路由生成 (`src/lib/route-utils.ts`)
- 自定义 Link 组件
- TypeScript 支持

## 开发命令

```bash
# 启动开发服务器
pnpm dev

# 构建内容
pnpm generate:content

# 生成 sitemap
pnpm generate:sitemap

# 生成 RSS
pnpm generate:rss

# 完整构建
pnpm build
```

## 组件使用

### MDX 渲染组件

```tsx
import { Mdx } from '@/components/mdx'

<Mdx code={content.body.code} />
```

### 类型安全链接

```tsx
import { SafeLink, BlogLink, ProductLink } from '@/components/ui/safe-link'

<BlogLink slug="my-post">阅读文章</BlogLink>
<ProductLink slug="my-product">查看产品</ProductLink>
```

## 配置文件

### contentlayer.config.ts

定义内容类型和字段结构

### next.config.mjs

集成 Contentlayer 和 MDX 支持

### scripts/

包含 SEO 自动化脚本

## 注意事项

1. **与现有系统共存**: 新的 MDX 系统不会影响现有的 posts 功能
2. **统一命名规范**: 所有内容类型使用复数形式和连字符分隔
3. **多语言支持**: 每种语言需要单独的 MDX 文件
4. **静态生成**: 所有 MDX 内容在构建时生成静态页面
5. **SEO 友好**: 自动生成 sitemap 和 RSS feeds

## 示例内容

项目中已包含以下示例内容：

- 英文博客：`getting-started-with-shipany.mdx`
- 中文博客：`shipany-kuai-su-ru-men.mdx`
- 英文产品：`ai-content-generator.mdx`
- 中文产品：`ai-nei-rong-sheng-cheng-qi.mdx`
- 英文案例：`techcorp-ai-transformation.mdx`
- 中文案例：`keji-gongsi-ai-zhuanxing.mdx`

## 扩展功能

系统设计为可扩展的，可以轻松添加：

- 新的内容类型
- 更多语言支持
- 自定义 MDX 组件
- 高级 SEO 功能

## 支持

如有问题，请参考：

1. Contentlayer 官方文档
2. Next.js 文档
3. MDX 文档
4. 项目中的示例代码
