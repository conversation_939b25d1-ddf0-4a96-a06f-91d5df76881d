# 语言切换功能测试指南

本文档详细说明如何测试新实现的智能语言切换功能。

## 功能概述

我们已经实现了两个主要功能：

### 1. 增强的全局语言切换器
- **位置**：头部导航栏的语言选择器
- **功能**：智能检测内容类型并提供相应的语言切换逻辑
- **增强**：支持内容页面的智能跳转和优雅降级

### 2. 内容页面语言版本指示器
- **位置**：博客、产品、案例研究详情页面的右上角
- **功能**：显示当前内容的可用语言版本
- **交互**：点击可切换到对应语言版本

## 测试场景

### 场景 1：完整语言版本切换

**测试内容**：`getting-started-with-shipany` 文章（英文和中文版本都存在）

**测试步骤**：
1. 访问英文版本：`http://localhost:3000/blogs/getting-started-with-shipany`
2. 观察页面右上角的语言版本指示器
3. 使用头部语言切换器切换到中文
4. 验证跳转到：`http://localhost:3000/zh/blogs/getting-started-with-shipany`
5. 使用语言版本指示器切换回英文
6. 验证跳转到：`http://localhost:3000/blogs/getting-started-with-shipany`

**预期结果**：
- ✅ 语言版本指示器显示两种语言都可用（绿色勾号）
- ✅ 切换无缝进行，无错误提示
- ✅ URL 正确更新
- ✅ 内容正确显示对应语言版本

### 场景 2：单语言内容的优雅降级

**测试内容**：`english-only-test` 文章（仅英文版本）

**测试步骤**：
1. 访问英文版本：`http://localhost:3000/blogs/english-only-test`
2. 观察语言版本指示器状态
3. 使用头部语言切换器切换到中文
4. 观察跳转行为和提示信息
5. 验证最终到达：`http://localhost:3000/zh/blogs`

**预期结果**：
- ✅ 语言版本指示器显示英文可用（绿色勾号），中文不可用（红色叉号）
- ✅ 切换到中文时显示 toast 提示信息
- ✅ 自动跳转到中文博客列表页面
- ✅ 提示信息说明内容不可用，已重定向到列表页

### 场景 3：非内容页面的语言切换

**测试内容**：首页、产品列表页等非具体内容页面

**测试步骤**：
1. 访问首页：`http://localhost:3000`
2. 使用头部语言切换器切换到中文
3. 验证跳转到：`http://localhost:3000/zh`
4. 访问产品列表：`http://localhost:3000/zh/products`
5. 切换回英文
6. 验证跳转到：`http://localhost:3000/products`

**预期结果**：
- ✅ 简单的路径替换逻辑正常工作
- ✅ 无错误提示
- ✅ URL 正确更新

### 场景 4：产品和案例研究页面测试

**测试内容**：产品和案例研究详情页面

**测试步骤**：
1. 访问产品页面：`http://localhost:3000/products/ai-content-generator`
2. 测试语言版本指示器
3. 访问案例研究页面：`http://localhost:3000/case-studies/techcorp-ai-transformation`
4. 测试语言版本指示器

**预期结果**：
- ✅ 语言版本指示器正常显示
- ✅ 语言切换功能正常工作

## 用户界面测试

### 语言版本指示器 UI 测试

**紧凑模式（compact）**：
- 显示在内容页面右上角
- 包含语言图标和语言按钮
- 绿色勾号表示可用，红色叉号表示不可用
- 当前语言高亮显示

**完整模式（full）**：
- 可以通过修改组件 variant 属性测试
- 显示更详细的语言版本信息
- 包含可用性状态和切换按钮

### 响应式设计测试

**测试步骤**：
1. 在桌面浏览器中测试
2. 调整浏览器窗口大小
3. 在移动设备上测试
4. 验证语言版本指示器在不同屏幕尺寸下的显示

**预期结果**：
- ✅ 在小屏幕上正确显示
- ✅ 不影响页面布局
- ✅ 触摸友好的交互

## 错误处理测试

### 无效 URL 测试

**测试步骤**：
1. 访问不存在的内容：`http://localhost:3000/blogs/non-existent-article`
2. 尝试语言切换
3. 验证错误处理

**预期结果**：
- ✅ 正确显示 404 页面
- ✅ 语言切换器仍然可用
- ✅ 切换到其他语言的首页或列表页

### 网络错误测试

**测试步骤**：
1. 模拟网络延迟
2. 在语言切换过程中测试用户体验
3. 验证加载状态和错误处理

## 性能测试

### 加载性能

**测试指标**：
- 语言版本指示器的渲染时间
- 语言切换的响应时间
- 页面跳转的速度

**测试方法**：
1. 使用浏览器开发者工具
2. 测量组件渲染时间
3. 测量路由跳转时间

### 内存使用

**测试步骤**：
1. 长时间使用语言切换功能
2. 监控内存使用情况
3. 检查是否有内存泄漏

## 自动化测试建议

### 单元测试

```javascript
// 测试语言切换逻辑
describe('Content Language Switching', () => {
  test('should detect content page correctly', () => {
    // 测试内容页面检测
  })
  
  test('should handle language switching with existing content', () => {
    // 测试存在目标语言版本的情况
  })
  
  test('should fallback gracefully when content not available', () => {
    // 测试优雅降级
  })
})
```

### 集成测试

```javascript
// 测试完整的语言切换流程
describe('Language Switching Integration', () => {
  test('should switch languages on content pages', () => {
    // 测试内容页面的语言切换
  })
  
  test('should show appropriate notifications', () => {
    // 测试提示信息
  })
})
```

### E2E 测试

```javascript
// 使用 Playwright 进行端到端测试
test('language switching user journey', async ({ page }) => {
  // 完整的用户语言切换流程测试
})
```

## 已知问题和限制

### 当前限制

1. **语言版本检测**：基于文件系统，需要构建时确定
2. **实时更新**：内容更新后需要重新构建
3. **缓存**：可能需要清除缓存才能看到更新

### 未来改进

1. **动态检测**：支持运行时动态检测语言版本
2. **更多语言**：扩展支持更多语言
3. **用户偏好**：记住用户的语言偏好
4. **SEO 优化**：添加更多 SEO 相关的语言切换功能

## 故障排除

### 常见问题

1. **语言版本指示器不显示**
   - 检查是否在内容页面
   - 验证内容类型检测逻辑

2. **语言切换不工作**
   - 检查控制台错误
   - 验证路由配置

3. **提示信息不显示**
   - 检查 toast 组件是否正确导入
   - 验证提示逻辑

### 调试技巧

1. 使用浏览器开发者工具查看网络请求
2. 检查控制台日志
3. 验证 URL 变化
4. 测试不同的内容类型

---

通过以上测试场景，可以全面验证智能语言切换功能的正确性和用户体验。
