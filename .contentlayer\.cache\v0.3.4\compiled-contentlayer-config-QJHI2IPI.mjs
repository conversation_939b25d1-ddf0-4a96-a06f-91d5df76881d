// contentlayer.config.ts
import { defineDocumentType, makeSource } from "contentlayer/source-files";
import fs from "fs";
import path from "path";
var makeCommon = (name, pattern) => ({
  name,
  // Match all MDX files in the specified pattern directory and subdirectories
  filePathPattern: `${pattern}/**/*.mdx`,
  contentType: "mdx",
  // Define the frontmatter fields that each MDX file should contain
  fields: {
    title: { type: "string", required: true },
    // Article/product title
    slug: { type: "string", required: true },
    // URL slug for routing
    description: { type: "string", required: false },
    // Meta description for SEO
    coverImage: { type: "string", required: false },
    // Hero/cover image URL
    author: { type: "string", required: false },
    // Author name
    authorImage: { type: "string", required: false },
    // Author avatar URL
    publishedAt: { type: "date", required: false },
    // Publication date
    featured: { type: "boolean", required: false, default: false },
    // Featured flag
    tags: { type: "list", of: { type: "string" }, required: false }
    // Content tags
  },
  // Computed fields are automatically generated based on file path and metadata
  computedFields: {
    // Extract language from file path (e.g., 'blogs/en/post.mdx' -> 'en')
    lang: {
      type: "string",
      resolve: (doc) => doc._raw.flattenedPath.split("/")[1]
    },
    // Generate the full URL path for the content
    url: {
      type: "string",
      resolve: (doc) => `/${doc._raw.flattenedPath.split("/")[0]}/${doc._raw.flattenedPath.split("/")[1]}/${doc.slug}`
    },
    // Get file creation time from filesystem
    createdAt: {
      type: "date",
      resolve: (doc) => fs.statSync(path.join("content", doc._raw.sourceFilePath)).birthtime
    }
  }
});
var Blog = defineDocumentType(() => makeCommon("Blog", "blogs"));
var Product = defineDocumentType(() => makeCommon("Product", "products"));
var CaseStudy = defineDocumentType(() => makeCommon("CaseStudy", "case-studies"));
var contentlayer_config_default = makeSource({
  contentDirPath: "content",
  // Root directory for content files
  documentTypes: [Blog, Product, CaseStudy]
  // Document types to process
});
export {
  Blog,
  CaseStudy,
  Product,
  contentlayer_config_default as default
};
//# sourceMappingURL=compiled-contentlayer-config-QJHI2IPI.mjs.map
