/**
 * Content Language Switching Utilities
 * 
 * This module provides utilities for intelligent language switching in content pages.
 * It can detect content types, find corresponding language versions, and handle
 * smart navigation between different language versions of the same content.
 */

import { allBlogs, allProducts, allCaseStudies } from 'contentlayer/generated'
import { Locale } from '@/i18n/locale'

/**
 * Content types supported by the system
 */
export type ContentType = 'blog' | 'product' | 'case-study' | 'other'

/**
 * Content page information extracted from pathname
 */
export interface ContentPageInfo {
  type: ContentType
  slug: string | null
  currentLocale: string
}

/**
 * Available language version information
 */
export interface LanguageVersion {
  locale: string
  exists: boolean
  url: string
}

/**
 * Detect if the current pathname is a content page and extract information
 * 
 * @param pathname - Current pathname (e.g., '/blogs/my-post', '/zh/products/my-product')
 * @param currentLocale - Current locale
 * @returns Content page information
 */
export function detectContentPage(pathname: string, currentLocale: string): ContentPageInfo {
  // Remove locale prefix from pathname for analysis
  let cleanPath = pathname
  if (currentLocale !== 'en' && pathname.startsWith(`/${currentLocale}`)) {
    cleanPath = pathname.replace(`/${currentLocale}`, '')
  }

  // Detect content type and extract slug
  if (cleanPath.startsWith('/blogs/')) {
    const slug = cleanPath.replace('/blogs/', '')
    return {
      type: 'blog',
      slug: slug || null,
      currentLocale
    }
  }

  if (cleanPath.startsWith('/products/')) {
    const slug = cleanPath.replace('/products/', '')
    return {
      type: 'product',
      slug: slug || null,
      currentLocale
    }
  }

  if (cleanPath.startsWith('/case-studies/')) {
    const slug = cleanPath.replace('/case-studies/', '')
    return {
      type: 'case-study',
      slug: slug || null,
      currentLocale
    }
  }

  return {
    type: 'other',
    slug: null,
    currentLocale
  }
}

/**
 * Check if content exists in a specific language
 * 
 * @param contentType - Type of content
 * @param slug - Content slug
 * @param locale - Target locale
 * @returns Whether the content exists in the target locale
 */
export function contentExistsInLocale(
  contentType: ContentType,
  slug: string,
  locale: string
): boolean {
  if (contentType === 'other' || !slug) {
    return false
  }

  switch (contentType) {
    case 'blog':
      return allBlogs.some(blog => blog.slug === slug && blog.lang === locale)
    
    case 'product':
      return allProducts.some(product => product.slug === slug && product.lang === locale)
    
    case 'case-study':
      return allCaseStudies.some(caseStudy => caseStudy.slug === slug && caseStudy.lang === locale)
    
    default:
      return false
  }
}

/**
 * Get all available language versions for a piece of content
 * 
 * @param contentType - Type of content
 * @param slug - Content slug
 * @param supportedLocales - List of supported locales
 * @returns Array of language versions with availability information
 */
export function getAvailableLanguageVersions(
  contentType: ContentType,
  slug: string,
  supportedLocales: string[]
): LanguageVersion[] {
  if (contentType === 'other' || !slug) {
    return supportedLocales.map(locale => ({
      locale,
      exists: false,
      url: generateContentUrl(contentType, slug, locale)
    }))
  }

  return supportedLocales.map(locale => {
    const exists = contentExistsInLocale(contentType, slug, locale)
    const url = generateContentUrl(contentType, slug, locale)
    
    return {
      locale,
      exists,
      url
    }
  })
}

/**
 * Generate URL for content in a specific locale
 * 
 * @param contentType - Type of content
 * @param slug - Content slug
 * @param locale - Target locale
 * @returns Generated URL
 */
export function generateContentUrl(
  contentType: ContentType,
  slug: string,
  locale: string
): string {
  if (contentType === 'other' || !slug) {
    return locale === 'en' ? '/' : `/${locale}`
  }

  const basePath = getContentBasePath(contentType)
  const localePrefix = locale === 'en' ? '' : `/${locale}`
  
  return `${localePrefix}${basePath}/${slug}`
}

/**
 * Get base path for content type
 * 
 * @param contentType - Type of content
 * @returns Base path
 */
function getContentBasePath(contentType: ContentType): string {
  switch (contentType) {
    case 'blog':
      return '/blogs'
    case 'product':
      return '/products'
    case 'case-study':
      return '/case-studies'
    default:
      return ''
  }
}

/**
 * Handle intelligent language switching for content pages
 * 
 * @param pathname - Current pathname
 * @param currentLocale - Current locale
 * @param targetLocale - Target locale
 * @param supportedLocales - List of supported locales
 * @returns Object with target URL and switch strategy
 */
export function handleContentLanguageSwitch(
  pathname: string,
  currentLocale: string,
  targetLocale: string,
  supportedLocales: string[]
): {
  url: string
  strategy: 'direct' | 'fallback-home' | 'fallback-list'
  reason?: string
} {
  const contentInfo = detectContentPage(pathname, currentLocale)
  
  // For non-content pages, use simple path replacement
  if (contentInfo.type === 'other') {
    const newPathName = pathname.replace(`/${currentLocale}`, `/${targetLocale}`)
    const finalPath = newPathName.startsWith(`/${targetLocale}`) 
      ? newPathName 
      : `/${targetLocale}${newPathName}`
    
    return {
      url: targetLocale === 'en' ? finalPath.replace('/en', '') : finalPath,
      strategy: 'direct'
    }
  }

  // For content pages, check if target version exists
  if (contentInfo.slug && contentExistsInLocale(contentInfo.type, contentInfo.slug, targetLocale)) {
    return {
      url: generateContentUrl(contentInfo.type, contentInfo.slug, targetLocale),
      strategy: 'direct'
    }
  }

  // If target version doesn't exist, fallback to content list page
  const listUrl = generateContentUrl(contentInfo.type, '', targetLocale).replace(/\/$/, '')
  
  return {
    url: listUrl || (targetLocale === 'en' ? '/' : `/${targetLocale}`),
    strategy: contentInfo.slug ? 'fallback-list' : 'fallback-home',
    reason: contentInfo.slug 
      ? `Content "${contentInfo.slug}" not available in target language`
      : 'Content list page'
  }
}

/**
 * Get content title for display purposes
 * 
 * @param contentType - Type of content
 * @param slug - Content slug
 * @param locale - Content locale
 * @returns Content title or null if not found
 */
export function getContentTitle(
  contentType: ContentType,
  slug: string,
  locale: string
): string | null {
  if (contentType === 'other' || !slug) {
    return null
  }

  switch (contentType) {
    case 'blog':
      const blog = allBlogs.find(b => b.slug === slug && b.lang === locale)
      return blog?.title || null
    
    case 'product':
      const product = allProducts.find(p => p.slug === slug && p.lang === locale)
      return product?.title || null
    
    case 'case-study':
      const caseStudy = allCaseStudies.find(c => c.slug === slug && c.lang === locale)
      return caseStudy?.title || null
    
    default:
      return null
  }
}
