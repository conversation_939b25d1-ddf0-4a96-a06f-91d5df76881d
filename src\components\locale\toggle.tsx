"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { useParams, usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";

import { MdLanguage } from "react-icons/md";
import { localeNames, locales } from "@/i18n/locale";
import { handleContentLanguageSwitch } from "@/lib/content-language-utils";

export default function ({ isIcon = false }: { isIcon?: boolean }) {
  const params = useParams();
  const locale = params.locale as string;
  const router = useRouter();
  const pathname = usePathname();

  const handleSwitchLanguage = (value: string) => {
    if (value !== locale) {
      // Use intelligent content language switching
      const switchResult = handleContentLanguageSwitch(
        pathname,
        locale,
        value,
        locales
      );

      // Navigate to the target URL
      router.push(switchResult.url);

      // Show user feedback for fallback scenarios
      if (switchResult.strategy === 'fallback-list') {
        toast.info(
          `This content is not available in ${localeNames[value]}. Redirected to the content list.`,
          {
            duration: 4000,
          }
        );
      } else if (switchResult.strategy === 'fallback-home') {
        toast.info(
          `Redirected to ${localeNames[value]} homepage.`,
          {
            duration: 3000,
          }
        );
      }
    }
  };

  return (
    <Select value={locale} onValueChange={handleSwitchLanguage}>
      <SelectTrigger className="flex items-center gap-2 border-none text-muted-foreground outline-hidden hover:bg-transparent focus:ring-0 focus:ring-offset-0">
        <MdLanguage className="text-xl" />
        {!isIcon && (
          <span className="hidden md:block">{localeNames[locale]}</span>
        )}
      </SelectTrigger>
      <SelectContent className="z-50 bg-background">
        {Object.keys(localeNames).map((key: string) => {
          const name = localeNames[key];
          return (
            <SelectItem className="cursor-pointer px-4" key={key} value={key}>
              {name}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
}
